import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import os

# 调试：打印当前工作目录
print("Current working directory:", os.getcwd())

# 指定文件路径（根据实际情况调整）
file_path = "UAV1_positions_and_error.csv"  # 相对路径
# 或者使用绝对路径：
# file_path = "e:\\资料\\2024国市创\\929\\Loc_result\\20250929_003918_ref=0_algo=chan+newton\\UAV1_positions_and_error.csv"

# 加载CSV文件
try:
    df = pd.read_csv(file_path)
    print("File loaded successfully.")
except FileNotFoundError as e:
    print(f"Error: {e}. Please check the file path or move the file to the current directory.")
    exit()

# 1. 计算二维定位误差
df['2D_error'] = np.sqrt((df['est_X'] - df['true_X'])**2 + (df['est_Y'] - df['true_Y'])**2)

# 2. 基本统计分析
print("=== 二维定位误差统计描述 ===")
error_stats = df['2D_error'].describe()
print(error_stats)

# 计算额外统计：中位数、标准差、偏度、峰度
median_error = df['2D_error'].median()
std_error = df['2D_error'].std()
skewness = stats.skew(df['2D_error'])
kurtosis = stats.kurtosis(df['2D_error'])
print(f"\n中位数: {median_error:.2f}")
print(f"标准差: {std_error:.2f}")
print(f"偏度 (Skewness): {skewness:.2f} (正偏表示右尾长，负偏表示左尾长)")
print(f"峰度 (Kurtosis): {kurtosis:.2f} (大于0表示更尖的分布)")

# 3. 异常值检测（使用IQR方法）
Q1 = df['2D_error'].quantile(0.25)
Q3 = df['2D_error'].quantile(0.75)
IQR = Q3 - Q1
outliers = df[(df['2D_error'] < (Q1 - 1.5 * IQR)) | (df['2D_error'] > (Q3 + 1.5 * IQR))]
print(f"\n异常值数量: {len(outliers)}")
if not outliers.empty:
    print("异常值样本:")
    print(outliers.head())

# 4. 可视化分析
sns.set(style="whitegrid")

# 4.1 时间序列误差图
plt.figure(figsize=(12, 6))
plt.plot(df['timestamp'], df['2D_error'], label='2D Error', color='blue')
plt.axhline(y=error_stats['mean'], color='red', linestyle='--', label='Mean 2D Error')
plt.title('2D Positioning Error over Time')
plt.xlabel('Timestamp')
plt.ylabel('2D Error (units)')
plt.legend()
plt.savefig('2D_error_time_series.png')
plt.show()

# 4.2 误差分布直方图
plt.figure(figsize=(10, 5))
sns.histplot(df['2D_error'], kde=True, bins=50, color='green')
plt.title('Distribution of 2D Positioning Errors')
plt.xlabel('2D Error')
plt.ylabel('Frequency')
plt.savefig('2D_error_histogram.png')
plt.show()

# 4.3 箱线图
plt.figure(figsize=(8, 5))
sns.boxplot(x=df['2D_error'], color='orange')
plt.title('Boxplot of 2D Positioning Errors')
plt.xlabel('2D Error')
plt.savefig('2D_error_boxplot.png')
plt.show()

# 4.4 累计分布函数 (CDF) 图
plt.figure(figsize=(10, 5))
sorted_errors = np.sort(df['2D_error'])
cdf = np.arange(1, len(sorted_errors) + 1) / len(sorted_errors)
plt.plot(sorted_errors, cdf, marker='.', linestyle='none')
plt.title('Cumulative Distribution Function (CDF) of 2D Errors')
plt.xlabel('2D Error')
plt.ylabel('Cumulative Probability')
plt.grid(True)
plt.savefig('2D_error_cdf.png')
plt.show()

# 5. 高级分析：误差与时间戳的相关性
correlation = df['timestamp'].corr(df['2D_error'])
print(f"\n二维误差与时间戳的相关系数: {correlation:.4f} (接近0表示无线性相关)")

# 6. 分段分析
df['segment'] = pd.cut(df['timestamp'], bins=10)
segment_means = df.groupby('segment')['2D_error'].mean()
print("\n分段平均二维误差:")
print(segment_means)

# 保存二维误差统计到CSV
error_stats.to_csv('2D_error_statistics.csv', header=True)
print("\n二维误差统计结果已保存到 '2D_error_statistics.csv'")