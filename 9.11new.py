import numpy as np
import pandas as pd
from scipy.constants import c
import time
from tqdm import tqdm
import os

# --- TDOA求解器全局参数配置 ---
TDOA_SOLVER_PARAMS = {
    'max_iter': 100,
    'tol': 1e-8,
    'lambda_reg': 1e-4,
    'lambda_init': 1e-4,
    'lambda_up': 5,
    'lambda_down': 0.2
}

# --- 数据加载 ---
def load_data(file_path):
    """
    从指定的CSV文件加载仿真数据
    
    参数:
        file_path (str): CSV文件的路径。
    
    返回:
        tuple: 包含以下元素的元组：
            - monitor_coords (np.ndarray): 监测站的三维坐标数组，形状为 (6, 3)。
            - uav_true_positions (dict): 包含每个无人机真实轨迹的字典，键为无人机ID（如'UAV1'），值为轨迹坐标数组（形状为 [n_steps, 3]）。
            - timestamps (np.ndarray): 时间戳数组。
            - formation_labels (np.ndarray): 阵型标签数组。
    """
    df = pd.read_csv(file_path) # 使用pandas读取CSV文件到DataFrame
    
    # 提取6个监测站的坐标
    monitor_coords = [] # 初始化列表用于存储监测站坐标
    for i in range(1, 7): # 循环遍历6个监测站
        x = df[f'Monitor{i}_X'].iloc[0] # 获取第i个监测站的X坐标 (只取第一行，因为是固定站)
        y = df[f'Monitor{i}_Y'].iloc[0] # 获取第i个监测站的Y坐标
        z = df[f'Monitor{i}_Z'].iloc[0] # 获取第i个监测站的Z坐标
        monitor_coords.append([x, y, z]) # 将坐标列表添加到monitor_coords中
    monitor_coords = np.array(monitor_coords) # 将列表转换为NumPy数组

    # 提取所有目标无人机的ID和轨迹
    # 通过列名中是否包含 'UAV' 来识别无人机数据
    uav_ids = [col.split('_')[0] for col in df.columns if 'UAV' in col]
    uav_ids = sorted(list(set(uav_ids))) # 去重并排序，得到所有无人机的唯一ID列表
    
    uav_true_positions = {} # 初始化字典用于存储每个无人机的轨迹
    for uav in uav_ids: # 遍历每个无人机ID
        # 提取该无人机在所有时间点的X, Y, Z坐标
        uav_true_positions[uav] = df[[f'{uav}_X', f'{uav}_Y', f'{uav}_Z']].values

    # 提取阵型标签
    formation_labels = df['Formation_Label'].values if 'Formation_Label' in df.columns else None

    return monitor_coords, uav_true_positions, df['timestamp'].values, formation_labels

# --- TDOA 定位算法核心 ---
def tdoa_residuals(est_pos, anchors, tdoa_meas, ref_idx=0):
    """
    计算TDOA定位中的残差向量。
    残差定义为：(估计位置到各站的距离差) - (TDOA测量距离差)。
    这个向量是牛顿迭代法等优化算法的目标函数。

    参数:
        est_pos (np.ndarray): 目标当前被估计的三维位置 [x, y, z]。
        anchors (np.ndarray): 所有观测站（基站）的三维坐标数组。
        tdoa_meas (np.ndarray): TDOA测量值（以距离差表示），此数组不包含参考站自身。
        ref_idx (int): 参考站在anchors数组中的索引。

    返回:
        np.ndarray: 残差向量。
    """
    # 计算估计位置到所有观测站的欧氏距离
    est_dist = np.linalg.norm(anchors - est_pos, axis=1)
    # 计算基于估计位置的TDOA值（相对于参考站的距离差）
    est_tdoa = est_dist - est_dist[ref_idx]
    
    # 从估计的TDOA中删除参考站对应的值(为0)，使其与测量值tdoa_meas的维度匹配
    est_tdoa_without_ref = np.delete(est_tdoa, ref_idx)
    
    # 返回估计TDOA与实际测量TDOA之间的差值，即残差
    return est_tdoa_without_ref - tdoa_meas

def jacobian_matrix(est_pos, anchors, ref_idx=0):
    """
    解析法计算TDOA残差函数相对于目标位置(x, y, z)的雅可比矩阵。
    雅可比矩阵在牛顿迭代法中用于确定下一步的迭代方向和步长。
    优化版本：使用矢量化操作避免循环。

    参数:
        est_pos (np.ndarray): 目标当前被估计的三维位置 [x, y, z]。
        anchors (np.ndarray): 所有观测站（基站）的三维坐标数组。
        ref_idx (int): 参考站在anchors数组中的索引。

    返回:
        np.ndarray: 雅可比矩阵，形状为 (n_anchors - 1, 3)。
    """
    # 矢量化计算所有距离
    diffs = est_pos - anchors  # (n_anchors, 3)
    est_dist = np.linalg.norm(diffs, axis=1)  # (n_anchors,)

    # 避免除零
    est_dist = np.maximum(est_dist, 1e-12)

    # 计算所有导数
    derivs = diffs / est_dist[:, np.newaxis]  # (n_anchors, 3)

    # 参考站导数
    ref_deriv = derivs[ref_idx]  # (3,)

    # 构建雅可比矩阵，排除参考站
    mask = np.arange(len(anchors)) != ref_idx
    J = derivs[mask] - ref_deriv  # (n_anchors-1, 3)

    return J

def newton_tdoa_solver(anchors, tdoa_meas, initial_guess, ref_idx=0,
                      max_iter=None, tol=None, lambda_reg=None):
    """
    使用牛顿迭代法（高斯-牛顿法）求解TDOA定位问题。

    参数:
        anchors (np.ndarray): 所有观测站的坐标。
        tdoa_meas (np.ndarray): TDOA测量值（距离差）。
        initial_guess (np.ndarray): 初始猜测的目标位置。
        ref_idx (int): 参考站索引。
        max_iter (int): 最大迭代次数。
        tol (float): 两次迭代误差变化小于该值时认为收敛。
        lambda_reg (float): 正则化项（阻尼因子），用于提高解的稳定性，防止矩阵奇异。

    返回:
        np.ndarray: 最终估计的目标位置 [x, y, z]。
    """
    # 使用全局参数作为默认值
    global TDOA_SOLVER_PARAMS
    if max_iter is None:
        max_iter = TDOA_SOLVER_PARAMS['max_iter']
    if tol is None:
        tol = TDOA_SOLVER_PARAMS['tol']
    if lambda_reg is None:
        lambda_reg = TDOA_SOLVER_PARAMS['lambda_reg']

    pos = np.array(initial_guess, dtype=np.float64).copy() # 确保数据类型为float64
    prev_error = np.inf # 初始化上一次迭代的误差为无穷大
    
    for _ in range(max_iter):
        # 1. 计算当前位置的残差向量
        residuals = tdoa_residuals(pos, anchors, tdoa_meas, ref_idx)
        # 2. 计算雅可比矩阵
        J = jacobian_matrix(pos, anchors, ref_idx)
        
        # 3. 构造高斯-牛顿法的线性方程组 J^T*J * step = -J^T * residuals
        #    添加一个小的正则化项 (lambda * I) 来增加数值稳定性，这类似于Levenberg-Marquardt方法
        JTJ = J.T @ J + lambda_reg * np.eye(3) 
        
        # 4. 解线性方程组，得到位置更新步长 step
        step = np.linalg.solve(JTJ, -J.T @ residuals)
        
        # 5. 更新位置
        pos += step
        
        # 6. 检查收敛性
        current_error = np.linalg.norm(residuals) # 当前残差的L2范数作为误差
        if abs(prev_error - current_error) < tol:
            break # 如果误差变化足够小，则停止迭代
        prev_error = current_error
        
    return pos

def newton_tdoa_solver_lm(anchors, tdoa_meas, initial_guess, ref_idx=0,
                         max_iter=None, tol=None, lambda_init=None,
                         lambda_up=None, lambda_down=None):
    """
    使用Levenberg-Marquardt算法求解TDOA定位问题。
    这是一种改进的牛顿法，具有自适应阻尼因子，能够在非二次曲面上更稳健地收敛。

    主要改进点：
    1. 自适应阻尼 λ：初始化 λ = 1e-3，每次步长没降误差就放大（*10），降了就缩小（*0.1），
       可防止过冲或停滞。
    2. 折返检查：在真正更新 pos 前，先试算一次 dp 后的新误差 new_err，只有当它更小才接受
       更新并缩小 λ，否则拒绝更新并放大 λ。
    3. 收敛更稳健：折返保证残差单调下降；动态 λ 保证当接近最优时步长足够小，远离时又能快速收敛。

    算法特点：
    - 当步长导致残差增大时，会自动提高 λ，退回到"更像梯度下降"的小步长（更稳）
    - 当残差持续下降时，会降低 λ，迅速收敛到二次近似的 Newton 速度（更快）

    参数:
        anchors (np.ndarray): 所有观测站的坐标。
        tdoa_meas (np.ndarray): TDOA测量值（距离差）。
        initial_guess (np.ndarray): 初始猜测的目标位置。
        ref_idx (int): 参考站索引。
        max_iter (int): 最大迭代次数。
        tol (float): 两次迭代误差变化小于该值时认为收敛。
        lambda_init (float): 初始阻尼因子。
        lambda_up (float): 阻尼因子增大倍数。
        lambda_down (float): 阻尼因子减小倍数。

    返回:
        tuple: (最终估计的目标位置 [x, y, z], 迭代次数)
    """
    # 使用全局参数作为默认值
    global TDOA_SOLVER_PARAMS
    if max_iter is None:
        max_iter = TDOA_SOLVER_PARAMS['max_iter']
    if tol is None:
        tol = TDOA_SOLVER_PARAMS['tol']
    if lambda_init is None:
        lambda_init = TDOA_SOLVER_PARAMS['lambda_init']
    if lambda_up is None:
        lambda_up = TDOA_SOLVER_PARAMS['lambda_up']
    if lambda_down is None:
        lambda_down = TDOA_SOLVER_PARAMS['lambda_down']

    pos = np.array(initial_guess, dtype=np.float64).copy()
    lamb = lambda_init
    prev_err = np.linalg.norm(tdoa_residuals(pos, anchors, tdoa_meas, ref_idx))

    iter_count = 0
    for _ in range(max_iter):
        iter_count += 1

        # 计算当前位置的残差向量和雅可比矩阵
        r = tdoa_residuals(pos, anchors, tdoa_meas, ref_idx)
        J = jacobian_matrix(pos, anchors, ref_idx)

        # 构造Levenberg-Marquardt方程：(J^T*J + λ*I) * dp = -J^T * r
        G = J.T @ J
        H = G + lamb * np.eye(3)
        dp = -np.linalg.solve(H, J.T @ r)

        # 试探新位置并计算新误差
        new_pos = pos + dp
        new_err = np.linalg.norm(tdoa_residuals(new_pos, anchors, tdoa_meas, ref_idx))

        # 自适应调整阻尼因子
        if new_err < prev_err:
            # 误差下降，接受更新并减小阻尼因子
            error_change = abs(prev_err - new_err)
            pos = new_pos
            prev_err = new_err
            lamb = max(lamb * lambda_down, 1e-12)  # 防止λ过小

            # 检查收敛性（只有在接受更新时才检查）
            if error_change < tol:
                break
        else:
            # 误差增大，拒绝更新并增大阻尼因子
            lamb *= lambda_up
            # 如果阻尼因子过大，可能陷入困境，此时也退出
            if lamb > 1e10:
                break

    return pos, iter_count

def chan_initial(anchors, tdoa_meas, ref_idx=0):
    """
    使用Chan氏算法（一种非迭代的解析算法）为TDOA定位提供一个良好的初始解。
    该方法通过将非线性方程组转换为线性方程组来求解。

    参数:
        anchors (np.ndarray): 观测站坐标。
        tdoa_meas (np.ndarray): TDOA测量值（距离差）。
        ref_idx (int): 参考站索引。

    返回:
        np.ndarray: Chan算法计算出的初始位置估计 [x, y, z]。
    """
    ref_anchor = anchors[ref_idx] # 参考站坐标
    M = anchors.shape[0] # 观测站总数
    
    A = [] # 线性方程组的系数矩阵
    d = [] # 线性方程组的右侧向量
    
    tdoa_idx = 0
    for i in range(M):
        if i == ref_idx:
            continue
            
        ai = anchors[i] # 当前观测站i的坐标
        ti = tdoa_meas[tdoa_idx] # 对应的TDOA距离差
        tdoa_idx += 1
        
        # 构造线性方程组的一行
        A.append(np.hstack((2 * (ai - ref_anchor), -2 * ti)))
        d.append(np.sum(ai**2) - np.sum(ref_anchor**2) - ti**2)
        
    A = np.array(A)
    d = np.array(d)
    
    # 使用最小二乘法求解 Ax = d，其中未知向量x包含 [x, y, z, r_ref]
    # r_ref 是目标到参考站的距离
    u, _, _, _ = np.linalg.lstsq(A, d, rcond=None)
    
    # 提取解向量的前三个元素作为位置估计
    x_init = u[:3]
    return x_init

    """
    计算单个目标点相对于当前基站布局的几何精度因子（GDOP）。
    GDOP = sqrt(trace((J^T * J)^-1))，值越小代表定位精度越高。
    优化版本：使用矢量化操作。

    参数:
        target_pos (np.ndarray): 单个目标的位置 [x, y, z]。
        anchors (np.ndarray): 全部观测站的坐标 (n_anchors x 3)。
    返回:
        float: GDOP值。
    """
    # 矢量化计算所有距离
    diffs = target_pos - anchors  # (n_anchors, 3)
    dists = np.linalg.norm(diffs, axis=1)  # (n_anchors,)

    # 检查是否有基站与目标重合
    if np.any(dists < 1e-9):
        return np.inf

    # 矢量化计算雅可比矩阵
    J = -diffs / dists[:, np.newaxis]  # (n_anchors, 3)

    try:
        # 计算 H = J^T * J
        H = J.T @ J
        # 为增加数值稳定性，在求逆前给对角线增加一个极小量
        H_inv = np.linalg.inv(H + np.eye(H.shape[0]) * 1e-9)
        # GDOP = sqrt(trace(H_inv))
        gdop_value = np.sqrt(np.abs(np.trace(H_inv))) # 取绝对值避免浮点误差导致极小的负数
    except np.linalg.LinAlgError:
        # 如果矩阵H奇异（例如所有基站共线或共面），则GDOP为无穷大
        gdop_value = np.inf

    return gdop_value

def calculate_average_gdop(anchors, airspace_grid):
    """
    计算给定基站布局在整个监视空域网格上的平均GDOP。
    这是差分进化算法的适应度函数（目标函数），我们需要最小化这个值。

    参数:
        anchors (np.ndarray): 全部观测站的坐标 (n_anchors x 3)。
        airspace_grid (np.ndarray): 监视空域的离散网格点坐标 (N_points x 3)。
    返回:
        float: 平均GDOP值。
    """
    # 使用NumPy的广播和矢量化操作来高效计算所有网格点的GDOP
    diffs = airspace_grid[:, np.newaxis, :] - anchors[np.newaxis, :, :]
    dists = np.linalg.norm(diffs, axis=2)
    
    if np.any(dists < 1e-9): # 检查是否有网格点与基站重合
        return np.inf
        
    # J的形状是 (N_points, n_anchors, 3)
    J = -diffs / dists[..., np.newaxis]
    
    # 一次性计算所有点的 H = J^T * J
    # 'nij,nik->njk' 表示对每个点n，计算J[n,:,:]^T @ J[n,:,:]
    H = np.einsum('nij,nik->njk', J, J) 
    
    try:
        # 一次性计算所有H矩阵的逆
        H_inv = np.linalg.inv(H + np.eye(H.shape[1]) * 1e-9)
        # 一次性计算所有逆矩阵的迹并求平方根
        # 'nii->n' 表示对每个点n，计算H_inv[n,:,:]的迹
        gdop_values = np.sqrt(np.abs(np.einsum('nii->n', H_inv)))
        return np.mean(gdop_values) # 返回所有网格点GDOP的平均值
    except np.linalg.LinAlgError:
        return np.inf

def _is_in_forbidden_zone(uav_positions, forbidden_zones):
    """
    辅助函数：检查一组无人机位置是否进入了任何一个禁飞区。
    """
    for zone in forbidden_zones:
        if zone['type'] == 'box':
            bounds = zone['bounds'] # [[x_min, y_min, z_min], [x_max, y_max, z_max]]
            for pos in uav_positions:
                if (bounds[0][0] <= pos[0] <= bounds[1][0] and
                    bounds[0][1] <= pos[1] <= bounds[1][1] and
                    bounds[0][2] <= pos[2] <= bounds[1][2]):
                    return True # 只要有一个无人机在禁飞区内，就返回True
    return False

def run_adaptive_de_optimization(fixed_anchors, initial_uav_positions, de_params, constraints, airspace_grid):
    """
    使用自适应差分进化算法优化无人机观测站位置
    """
    # 解包参数
    NP = de_params['NP']
    G_max = de_params['G_max']
    F0 = de_params['F0']
    patience = de_params['patience']
    tolerance = de_params['tolerance']
    
    # --- 1. 初始化种群 ---
    search_space = constraints['search_space']
    forbidden_zones = constraints.get('forbidden_zones', [])
    
    population = np.zeros((NP, 6))  # 2个无人机 × 3个坐标
    for i in range(NP):
        while True:
            candidate = np.array([
                np.random.uniform(search_space['x'][0], search_space['x'][1]),
                np.random.uniform(search_space['y'][0], search_space['y'][1]),
                np.random.uniform(search_space['z'][0], search_space['z'][1]),
                np.random.uniform(search_space['x'][0], search_space['x'][1]),
                np.random.uniform(search_space['y'][0], search_space['y'][1]),
                np.random.uniform(search_space['z'][0], search_space['z'][1])
            ])
            uav_positions = candidate.reshape(2, 3)
            if not _is_in_forbidden_zone(uav_positions, forbidden_zones):
                population[i] = candidate
                break
    
    # 初始化适应度和历史记录
    fitness = np.full(NP, np.inf)
    fitness_history = []
    best_fitness_so_far = np.inf
    best_solution = population[0]
    generations_run = 0
    stop_reason = "Max generations reached"
    
    # --- 2. DE主循环（不显示进度条，避免干扰主进度条） ---
    for g in range(G_max):
        generations_run += 1
        
        # a. 评估种群中每个个体的适应度
        for i in range(NP):
            uav_pos = population[i].reshape(2, 3)
            current_anchors = np.vstack((fixed_anchors, uav_pos))
            fitness[i] = calculate_average_gdop(current_anchors, airspace_grid)

        # b. 记录当前代的最佳结果并检查是否收敛
        best_idx = np.argmin(fitness)
        if fitness[best_idx] < best_fitness_so_far:
            best_fitness_so_far = fitness[best_idx]
            best_solution = population[best_idx]
        
        fitness_history.append(best_fitness_so_far)
        
        # 早停机制
        if g > patience:
            if (fitness_history[-patience] - best_fitness_so_far) < tolerance:
                stop_reason = "Converged"
                break
        
        # c. 自适应更新DE的控制参数
        lambda_val = np.exp(1 - G_max / (G_max + 1 - (g + 1)))
        F = F0 * (2 ** lambda_val)
        CR = 0.5 * (1 + np.random.rand())
        
        # d. 变异、交叉和选择
        new_population = population.copy()
        for i in range(NP):
            # 变异
            indices = np.random.choice([j for j in range(NP) if j != i], 3, replace=False)
            mutant_vec = population[indices[0]] + F * (population[indices[1]] - population[indices[2]])
            
            # 交叉
            trial_vec = population[i].copy()
            for j in range(6):
                if np.random.rand() < CR:
                    trial_vec[j] = mutant_vec[j]
            
            # 边界处理
            trial_vec[0] = np.clip(trial_vec[0], search_space['x'][0], search_space['x'][1])
            trial_vec[1] = np.clip(trial_vec[1], search_space['y'][0], search_space['y'][1])
            trial_vec[2] = np.clip(trial_vec[2], search_space['z'][0], search_space['z'][1])
            trial_vec[3] = np.clip(trial_vec[3], search_space['x'][0], search_space['x'][1])
            trial_vec[4] = np.clip(trial_vec[4], search_space['y'][0], search_space['y'][1])
            trial_vec[5] = np.clip(trial_vec[5], search_space['z'][0], search_space['z'][1])
            
            # 选择
            trial_uavs = trial_vec.reshape(2, 3)
            if not _is_in_forbidden_zone(trial_uavs, forbidden_zones):
                trial_anchors = np.vstack((fixed_anchors, trial_uavs))
                trial_fitness = calculate_average_gdop(trial_anchors, airspace_grid)
                if trial_fitness < fitness[i]:
                    new_population[i] = trial_vec
        
        population = new_population
    
    # --- 3. 返回结果 ---
    results = {
        'best_positions': best_solution.reshape(2, 3),
        'best_fitness': best_fitness_so_far,
        'fitness_history': fitness_history,
        'generations_run': generations_run,
        'stop_reason': stop_reason
    }
    
    return results

# --- 辅助与仿真流程函数 ---
def calculate_tdoa_measurements(anchors, true_pos, ref_idx=0, noise_std=0.0):
    """
    根据目标真实位置和观测站布局，模拟生成TDOA测量值，并可选择添加高斯噪声。

    参数:
        anchors (np.ndarray): 观测站坐标。
        true_pos (np.ndarray): 目标的真实位置。
        ref_idx (int): 参考站索引。
        noise_std (float): 添加到TDOA距离差上的高斯噪声的标准差（单位：米）。

    返回:
        np.ndarray: 带或不带噪声的TDOA测量值（距离差）。
    """
    # 1. 计算信号从真实位置到达每个观测站的真实飞行时间
    arrival_times = np.linalg.norm(anchors - true_pos, axis=1) / c
    # 2. 计算真实的TDOA（时间差），并转换为距离差
    tdoa_dist_diff = (arrival_times - arrival_times[ref_idx]) * c
    
    # 3. 如果指定了噪声标准差，则添加高斯噪声
    if noise_std > 0:
        # 噪声加在距离差上
        noise = np.random.normal(0, noise_std, size=tdoa_dist_diff.shape)
        tdoa_dist_diff += noise
    
    # 4. 删除参考站对应的元素（其值为0），以匹配求解器的输入格式
    tdoa_without_ref = np.delete(tdoa_dist_diff, ref_idx)
    return tdoa_without_ref

# --- 结果分析与可视化 ---
 
def generate_dynamic_airspace_grid(target_positions, grid_density=5):
    """
    根据目标位置动态生成监视空域网格
    
    参数:
        target_positions (np.ndarray): 目标位置数组，形状为 (n_targets, 3)
        grid_density (int): 网格密度，每个维度的网格点数量
    
    返回:
        np.ndarray: 动态空域网格点坐标 (N_points x 3)
    """
    # 计算目标位置的包围盒
    min_coords = np.min(target_positions, axis=0)
    max_coords = np.max(target_positions, axis=0)
    
    # 添加边距
    margin = 5000  # 5公里边距
    min_coords -= margin
    max_coords += margin
    
    # 生成网格
    x_pts = np.linspace(min_coords[0], max_coords[0], grid_density)
    y_pts = np.linspace(min_coords[1], max_coords[1], grid_density)
    z_pts = np.linspace(min_coords[2], max_coords[2], max(3, grid_density//2))
    
    xv, yv, zv = np.meshgrid(x_pts, y_pts, z_pts, indexing='ij')
    dynamic_airspace_grid = np.vstack([xv.ravel(), yv.ravel(), zv.ravel()]).T
    
    return dynamic_airspace_grid

def generate_tdoa_measurements(anchors, true_pos, ref_idx=0, noise_std=0.0):
    """
    生成TDOA测量值（这是calculate_tdoa_measurements的别名）
    
    参数:
        anchors (np.ndarray): 观测站坐标
        true_pos (np.ndarray): 目标的真实位置
        ref_idx (int): 参考站索引
        noise_std (float): 噪声标准差
    
    返回:
        np.ndarray: TDOA测量值
    """
    return calculate_tdoa_measurements(anchors, true_pos, ref_idx, noise_std)

def main():
    # === 全局参数定义 ===
    REF_STATION_INDEX = 0
    TDOA_NOISE_STD = 1.5  # 固定噪声标准差
    OPTIMIZE_EVERY_N_STEPS = 100  # 优化频率

    # 固定仅使用 Chan + Newton 方法
    TDOA_SOLVER_ALGORITHM = 'chan+newton'
    
    # # 差分进化算法参数
    DE_PARAMS = {
        'NP': 15,
        'G_max': 100,
        'F0': 0.5,
        'patience': 20,
        'tolerance': 1e-4
    }
   
    # 物理约束参数
    CONSTRAINTS = {
        'search_space': {
            'x': [400000, 480000], 
            'y': [4080000, 4150000], 
            'z': [500, 15000]
        },
        'forbidden_zones': [
            {'type': 'box', 'bounds': ([440000, 4090000, -100], [450000, 4100000, 300])}
        ],
        'Vmax': 15.0,  # 无人机最大飞行速度 (米/秒)
        'dt': 1.0      # 时间步长(秒), 稍后会从数据中自动计算
    }
    
    # === 数据加载 ===
    SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
    DATA_FILE_NAME = "uav_3d_traj_with_formation_curve_path2(uav_num=5).csv"
    formation_data_path = os.path.join(SCRIPT_DIR, DATA_FILE_NAME)
    
    print("="*80)
    print("TDOA Positioning System - Formation Analysis with 4 Fixed + 2 Mobile Stations")
    print("="*80)
    
    if not os.path.exists(formation_data_path):
        print(f"Data file not found: {formation_data_path}")
        return
    
    # --- 1. 初始化和加载数据 ---
    # 生成基于日期时间的输出文件夹名称
    from datetime import datetime
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir_name = f"Loc_result/{current_time}_ref={REF_STATION_INDEX}_algo={TDOA_SOLVER_ALGORITHM}"
    output_dir = os.path.join(SCRIPT_DIR, output_dir_name)
    os.makedirs(output_dir, exist_ok=True)
    
    all_anchors, uav_true_positions, timestamps, formation_labels = load_data(formation_data_path)
    CONSTRAINTS['dt'] = np.mean(np.diff(timestamps)) if len(timestamps) > 1 else 1.0
    
    print(f"Data loaded successfully: {len(all_anchors)} monitors, {len(uav_true_positions)} target UAVs.")
    print(f"Using Monitor #{REF_STATION_INDEX + 1} as the reference station.")
    
    # --- 2. 初始化变量 ---
    fixed_anchors = all_anchors[:4]
    uav_anchors = all_anchors[4:6]
    uav_ids = list(uav_true_positions.keys())
    num_time_steps = len(timestamps)
    
    all_est_trajs = {uav_id: [] for uav_id in uav_ids}
    all_solve_times = {uav_id: [] for uav_id in uav_ids}
    prev_estimates = {uav_id: None for uav_id in uav_ids}
    
    uav5_trajectory_history = []
    uav6_trajectory_history = []
    
    optimizer_start_pos = uav_anchors.copy()
    optimizer_target_pos = uav_anchors.copy()
    steps_since_last_opt = 0
    
    # --- 3. 主循环：使用单一进度条 ---
    print(f"\n开始进行 {num_time_steps} 个时间步的TDOA定位...")
    
    # 主循环进度条
    with tqdm(total=num_time_steps, desc="TDOA定位进度", unit="步") as pbar:
        for time_idx in range(num_time_steps):
            
            # a. 周期性地执行观测站布局优化
            if time_idx > 0 and time_idx % OPTIMIZE_EVERY_N_STEPS == 0:
                pbar.set_description(f"[时间步 {time_idx}] 执行观测站布局优化")
                
                # --- 这里是核心修改 ---
                # 检查 prev_estimates 字典中的值是否都已经被填充（即至少完成了一次定位）
                if all(pos is not None for pos in prev_estimates.values()):
                    
                    # 使用【上一时刻所有目标的估计位置】作为本次优化的依据
                    current_target_positions = np.array(list(prev_estimates.values()))
                    
                    # 基于估计的目标位置，动态生成监视空域网格
                    dynamic_airspace_grid = generate_dynamic_airspace_grid(current_target_positions, grid_density=5)
                    
                    # 执行DE优化
                    optimizer_start_pos = uav_anchors.copy()
                    opt_results = run_adaptive_de_optimization(fixed_anchors, optimizer_start_pos, DE_PARAMS, CONSTRAINTS, dynamic_airspace_grid)
                    optimizer_target_pos = opt_results['best_positions']
                    steps_since_last_opt = 0
                    
                    pbar.write(f"优化完成。新目标位置: UAV5: {np.round(optimizer_target_pos[0], 2).tolist()}, UAV6: {np.round(optimizer_target_pos[1], 2).tolist()}")
                
                else:
                    # 如果还没有有效的估计位置（例如在第一次优化时），则跳过本次优化
                    pbar.write(f"[时间步 {time_idx}] 尚无有效估计位置，跳过本次布局优化。")
                
                # 将进度条描述改回正常状态
                pbar.set_description("TDOA定位进度")

            # b. 更新优化无人机位置（考虑最大速度约束的线性插值）
            if time_idx > 0:
                # 计算时间步长
                dt = CONSTRAINTS['dt']
                max_distance = CONSTRAINTS['Vmax'] * dt  # 最大移动距离

                # 计算目标位置与当前位置的距离
                distance_to_target = np.linalg.norm(optimizer_target_pos - uav_anchors, axis=1)

                # 对每个移动基站分别处理速度约束
                new_uav_anchors = np.zeros_like(uav_anchors)
                for i in range(len(uav_anchors)):
                    if distance_to_target[i] <= max_distance:
                        # 如果距离小于最大移动距离，直接到达目标位置
                        new_uav_anchors[i] = optimizer_target_pos[i]
                    else:
                        # 如果距离大于最大移动距离，按最大速度移动
                        direction = (optimizer_target_pos[i] - uav_anchors[i]) / distance_to_target[i]
                        new_uav_anchors[i] = uav_anchors[i] + direction * max_distance

                uav_anchors = new_uav_anchors
            
            steps_since_last_opt += 1
            
            # c. 记录优化无人机轨迹
            current_anchors = np.vstack((fixed_anchors, uav_anchors))
            uav5_trajectory_history.append(uav_anchors[0])
            uav6_trajectory_history.append(uav_anchors[1])
            
            # d. 对每个目标无人机进行TDOA定位
            for uav_id in uav_ids:
                true_pos = uav_true_positions[uav_id][time_idx]
                
                # 生成TDOA测量值
                tdoa_meas = generate_tdoa_measurements(current_anchors, true_pos, REF_STATION_INDEX, TDOA_NOISE_STD)
                
                # 设置初始猜测
                if prev_estimates[uav_id] is not None:
                    initial_guess_for_newton = prev_estimates[uav_id]
                else:
                    initial_guess_for_newton = chan_initial(current_anchors, tdoa_meas, REF_STATION_INDEX)

                # 仅运行 Chan + lm 方法
                start_solve_time = time.time()
                try:
                    chan_guess = chan_initial(current_anchors, tdoa_meas, REF_STATION_INDEX)
                    est_pos, iter_count = newton_tdoa_solver_lm(current_anchors, tdoa_meas, chan_guess, REF_STATION_INDEX)
                except Exception:
                    est_pos, iter_count = newton_tdoa_solver_lm(current_anchors, tdoa_meas, initial_guess_for_newton, REF_STATION_INDEX)

                solve_time = time.time() - start_solve_time

                # 记录结果
                all_est_trajs[uav_id].append(est_pos)
                all_solve_times[uav_id].append(solve_time)
                prev_estimates[uav_id] = est_pos
            
            # 更新进度条
            pbar.update(1)
    
    # --- 4. 仅保存定位结果与误差，以及移动站坐标 CSV ---
    for uav_id in uav_ids:
        true_traj = np.array(uav_true_positions[uav_id])
        est_traj = np.array(all_est_trajs[uav_id])
        total_error = np.linalg.norm(true_traj - est_traj, axis=1)
        df = pd.DataFrame({
            'timestamp': timestamps,
            'true_X': true_traj[:, 0], 'true_Y': true_traj[:, 1], 'true_Z': true_traj[:, 2],
            'est_X': est_traj[:, 0], 'est_Y': est_traj[:, 1], 'est_Z': est_traj[:, 2],
            'total_error': total_error
        })
        df.to_csv(os.path.join(output_dir, f"{uav_id}_positions_and_error.csv"), index=False)

    uav5_hist = np.array(uav5_trajectory_history)
    uav6_hist = np.array(uav6_trajectory_history)
    max_len = len(timestamps)
    uav5_hist = uav5_hist[:max_len]
    uav6_hist = uav6_hist[:max_len]
    mobile_df = pd.DataFrame({
        'timestamp': timestamps[:len(uav5_hist)],
        'UAV5_X': uav5_hist[:, 0], 'UAV5_Y': uav5_hist[:, 1], 'UAV5_Z': uav5_hist[:, 2],
        'UAV6_X': uav6_hist[:, 0], 'UAV6_Y': uav6_hist[:, 1], 'UAV6_Z': uav6_hist[:, 2]
    })
    mobile_df.to_csv(os.path.join(output_dir, "mobile_station_coordinates.csv"), index=False)

    print(f"定位结果与误差CSV已保存至目录: {output_dir}")

if __name__ == "__main__":
    main()
